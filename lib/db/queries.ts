import 'server-only';

import { hash } from 'bcryptjs';
import { and, asc, desc, eq, gt, gte, lt, ne, sql, notInArray, notExists } from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import crypto from 'crypto';
import retry from 'async-retry';

import {
  user,
  chat,
  type User,
  document,
  type Suggestion,
  suggestion,
  type MessageInsert,
  message,
  type DebugMessageInsert,
  debugMessage,
  vote,
  refreshToken,
  messageEmotion,
  debugChat,
  batches,
  userMessageCursor,
  nodes,
  edges,
  recollections,
  dismissedRecollections,
  userMessageVisibility,
  type UserMessageVisibilityInsert,
  clearedMessages,
  type ClearedMessagesInsert,
} from './schema';

// Optionally, if not using email/pass login, you can
// use the Drizzle adapter for Auth.js / NextAuth
// https://authjs.dev/reference/adapter/drizzle

// Database connection configuration optimized for serverless environments
const connectionConfig = {
  // Connection pooling configuration - optimized for serverless
  max: process.env.NODE_ENV === 'production' ? 15 : 2, // Increased for better concurrency
  idle_timeout: 15, // Reduced to free up connections faster
  connect_timeout: 10, // Reduced for faster failure detection

  // Retry and lifetime configuration
  max_lifetime: 60 * 20, // Reduced to 20 minutes for better connection cycling

  // Connection options
  connection: {
    search_path: 'public,profile',
    application_name: 'gentlegossip-app',
  },

  // SSL configuration - always require SSL for Neon
  ssl: 'require' as const,

  // Transform configuration for better error handling
  transform: {
    undefined: null,
  },

  // Prepared statements - enable in production for better performance
  prepare: process.env.NODE_ENV === 'production',

  // Debug mode for development
  debug: process.env.NODE_ENV === 'development' ? true : false,

  // Additional serverless optimizations
  onnotice: process.env.NODE_ENV === 'development' ? console.log : () => {},
  onparameter: process.env.NODE_ENV === 'development' ? console.log : () => {},

  // Connection keep-alive settings (in seconds) - optimized for serverless
  keep_alive: 20, // Reduced for faster cleanup

  // Additional performance optimizations
  fetch_types: false, // Disable automatic type fetching for better performance

  // Memory and performance settings
  types: {
    // Optimize common types for better performance
    // eslint-disable-next-line import/no-named-as-default-member
    bigint: postgres.BigInt,
  },

  // Error handling optimizations
  onclose: function(connection_id: number) {
    if (process.env.NODE_ENV === 'development') {
      console.log(`Connection ${connection_id} closed`);
    }
  },

  // Connection pool health monitoring
  onconnect: async function(connection: any) {
    if (process.env.NODE_ENV === 'development') {
      console.log('New database connection established');
    }
    try {
      await connection.unsafe(`
        SET statement_timeout = '30s';
        SET lock_timeout = '10s';
        SET idle_in_transaction_session_timeout = '60s';
        SET tcp_keepalives_idle = 600;
        SET tcp_keepalives_interval = 30;
        SET tcp_keepalives_count = 3;
        SET work_mem = '4MB';
        SET maintenance_work_mem = '64MB';
        SET effective_cache_size = '1GB';
        SET random_page_cost = 1.1;
        SET seq_page_cost = 1.0;
      `);
    } catch (error) {
      console.warn('Failed to set connection parameters:', error);
      // Continue without custom parameters
    }
  },
};

// biome-ignore lint: Forbidden non-null assertion.
const client = postgres(process.env.POSTGRES_URL!, connectionConfig);

const db = drizzle(client);

// Add after db initialization
let isWarmed = false;

export async function warmConnection() {
  if (!isWarmed && process.env.NODE_ENV === 'production') {
    try {
      await db.execute(sql`SELECT 1`);
      isWarmed = true;
      console.log('Database connection warmed');
    } catch (error) {
      console.warn('Failed to warm connection:', error);
    }
  }
}

// Retry wrapper for database operations
async function withRetry<T>(
  operation: () => Promise<T>,
  operationName: string,
  maxRetries = 3
): Promise<T> {
  return retry(
    async (bail) => {
      try {
        return await operation();
      } catch (error: any) {
        // Don't retry on certain types of errors
        if (
          error?.code === 'ENOTFOUND' ||
          error?.code === 'ECONNREFUSED' ||
          error?.message?.includes('authentication') ||
          error?.message?.includes('permission')
        ) {
          bail(error);
          return Promise.reject(error);
        }

        // Log retry attempts with more context
        console.warn(`Database operation "${operationName}" failed, retrying...`, {
          error: error?.message,
          code: error?.code,
          errno: error?.errno,
          attempt: error?.attemptNumber || 1,
          isConnectionClosed: error?.code === 'CONNECTION_CLOSED' || error?.errno === 'CONNECTION_CLOSED',
        });

        // For CONNECTION_CLOSED errors, add a small delay before retry
        if (error?.code === 'CONNECTION_CLOSED' || error?.errno === 'CONNECTION_CLOSED') {
          await new Promise(resolve => setTimeout(resolve, 500));
        }

        throw error;
      }
    },
    {
      retries: maxRetries,
      factor: 2,
      minTimeout: 1000,
      maxTimeout: 5000,
      randomize: false, // Disable randomization to prevent negative timeout calculations
    }
  );
}

export async function getUser(email: string): Promise<Array<User>> {
  try {
    return await db.select().from(user).where(eq(user.email, email));
  } catch (error) {
    console.error('Failed to get user from database');
    throw error;
  }
}

export async function createUser(email: string, password: string) {
  try {
    const hashedPassword = await hash(password, 10);
    return await db
      .insert(user)
      .values({ email, password: hashedPassword })
      .returning();
  } catch (error) {
    console.error('Failed to create user in database');
    throw error;
  }
}

export async function saveChat({
  id,
  userId,
  title,
  isVoiceChat,
}: {
  id: string;
  userId: string;
  title: string;
  isVoiceChat?: boolean;
}) {
  try {
    return await db.insert(chat).values({
      id,
      userId,
      title,
      isVoiceChat: isVoiceChat ?? false,
    });
  } catch (error) {
    console.error('Failed to save chat in database');
    throw error;
  }
}

export async function deleteChatById({ id }: { id: string }) {
  try {
    // Soft delete by updating the deleted column instead of removing the record
    return await db.update(chat).set({ deleted: true }).where(eq(chat.id, id));
  } catch (error) {
    console.error('Failed to soft delete chat by id from database');
    throw error;
  }
}

export async function getChatsByUserId({ id }: { id: string }) {
  return withRetry(
    async () => {
      return await db
        .select()
        .from(chat)
        .where(and(eq(chat.userId, id), eq(chat.deleted, false)))
        .orderBy(desc(chat.createdAt));
    },
    'getChatsByUserId'
  ).catch((error) => {
    console.error('Failed to get chats by user from database', {
      userId: id,
      error: error?.message,
      code: error?.code,
    });
    throw error;
  });
}

export async function getChatById({ id }: { id: string }) {
  return withRetry(
    async () => {
      const [selectedChat] = await db
        .select()
        .from(chat)
        .where(and(eq(chat.id, id), eq(chat.deleted, false)));
      return selectedChat;
    },
    'getChatById'
  ).catch((error) => {
    console.error('Failed to get chat by id from database', {
      chatId: id,
      error: error?.message,
      code: error?.code,
    });
    throw error;
  });
}

export async function saveMessages({ messages }: { messages: Array<MessageInsert> }) {
  try {
    return await db.insert(message).values(messages);
  } catch (error) {
    console.error('Failed to save messages in database', error);
    throw error;
  }
}

export async function getMessagesByChatId({
  id,
  userId,
  limit = 1000, // Default limit
  offset = 0, // Default offset
  orderBy = 'desc', // Default order
}: {
  id: string;
  userId?: string; // Optional userId to respect cleared messages
  limit?: number; // Make limit optional
  offset?: number; // Make offset optional
  orderBy?: 'asc' | 'desc'; // Make orderBy optional
}) {
  return withRetry(
    async () => {
      const whereConditions = [
        eq(message.chatId, id),
        eq(chat.deleted, false)
      ];

      // If userId is provided, check for cleared messages
      if (userId) {
        // Get user's cleared messages settings
        const [clearedSettings] = await db
          .select()
          .from(clearedMessages)
          .where(eq(clearedMessages.userId, userId))
          .orderBy(desc(clearedMessages.clearedAt))
          .limit(1);

        // If user has cleared messages, only show messages after the last cleared message
        // and exclude the last cleared message itself
        if (clearedSettings?.lastClearedMessageId) {
          // Get the timestamp of the last cleared message
          const [lastClearedMessage] = await db
            .select({ createdAt: message.createdAt })
            .from(message)
            .where(eq(message.id, clearedSettings.lastClearedMessageId))
            .limit(1);

          if (lastClearedMessage?.createdAt) {
            // Use gte (greater than or equal) to include messages at the same timestamp
            // but exclude the specific cleared message by ID
            whereConditions.push(gte(message.createdAt, lastClearedMessage.createdAt));
            whereConditions.push(ne(message.id, clearedSettings.lastClearedMessageId));
          }
        }
      }

      return await db
        .select({
          id: message.id,
          createdAt: message.createdAt,
          chatId: message.chatId,
          role: message.role,
          content: message.content,
          modelId: message.modelId,
          systemPromptId: message.systemPromptId,
        })
        .from(message)
        .innerJoin(chat, eq(message.chatId, chat.id))
        .where(and(...whereConditions))
        .limit(limit)
        .offset(offset)
        .orderBy(
          orderBy === 'asc' ? asc(message.createdAt) : desc(message.createdAt),
        );
    },
    'getMessagesByChatId'
  ).catch((error) => {
    console.error('Failed to get messages by chat id from database', {
      chatId: id,
      userId,
      limit,
      offset,
      orderBy,
      error: error?.message,
      code: error?.code,
    });
    throw error;
  });
}

export async function voteMessage({
  chatId,
  messageId,
  type,
}: {
  chatId: string;
  messageId: string;
  type: 'up' | 'down';
}) {
  try {
    const [existingVote] = await db
      .select()
      .from(vote)
      .where(and(eq(vote.messageId, messageId)));

    if (existingVote) {
      return await db
        .update(vote)
        .set({ isUpvoted: type === 'up' })
        .where(and(eq(vote.messageId, messageId), eq(vote.chatId, chatId)));
    }
    return await db.insert(vote).values({
      chatId,
      messageId,
      isUpvoted: type === 'up',
    });
  } catch (error) {
    console.error('Failed to upvote message in database', error);
    throw error;
  }
}

export async function getVotesByChatId({ id }: { id: string }) {
  try {
    return await db.select().from(vote).where(eq(vote.chatId, id));
  } catch (error) {
    console.error('Failed to get votes by chat id from database', error);
    throw error;
  }
}

export async function saveDocument({
  id,
  title,
  content,
  userId,
}: {
  id: string;
  title: string;
  content: string;
  userId: string;
}) {
  try {
    return await db.insert(document).values({
      id,
      title,
      content,
      userId,
      createdAt: new Date(),
    });
  } catch (error) {
    console.error('Failed to save document in database');
    throw error;
  }
}

export async function getDocumentsById({ id }: { id: string }) {
  try {
    // Check if any row with this document ID has deleted=true
    const hasDeletedRow = await db
      .select({ id: document.id })
      .from(document)
      .where(and(eq(document.id, id), eq(document.deleted, true)))
      .limit(1);

    // If any row is deleted, return empty array
    if (hasDeletedRow.length > 0) {
      return [];
    }

    // Otherwise, return all non-deleted rows for this document ID
    const documents = await db
      .select()
      .from(document)
      .where(and(eq(document.id, id), eq(document.deleted, false)))
      .orderBy(asc(document.createdAt));

    return documents;
  } catch (error) {
    console.error('Failed to get document by id from database');
    throw error;
  }
}

export async function getDocumentById({ id }: { id: string }) {
  try {
    // Check if any row with this document ID has deleted=true
    const hasDeletedRow = await db
      .select({ id: document.id })
      .from(document)
      .where(and(eq(document.id, id), eq(document.deleted, true)))
      .limit(1);

    // If any row is deleted, return undefined
    if (hasDeletedRow.length > 0) {
      return undefined;
    }

    // Otherwise, return the latest non-deleted row for this document ID
    const [selectedDocument] = await db
      .select()
      .from(document)
      .where(and(eq(document.id, id), eq(document.deleted, false)))
      .orderBy(desc(document.createdAt));

    return selectedDocument;
  } catch (error) {
    console.error('Failed to get document by id from database');
    throw error;
  }
}

export async function deleteDocumentsByIdAfterTimestamp({
  id,
  timestamp,
}: {
  id: string;
  timestamp: Date;
}) {
  try {
    await db
      .delete(suggestion)
      .where(
        and(
          eq(suggestion.documentId, id),
          gt(suggestion.documentCreatedAt, timestamp),
        ),
      );

    return await db
      .delete(document)
      .where(and(eq(document.id, id), gt(document.createdAt, timestamp)));
  } catch (error) {
    console.error(
      'Failed to delete documents by id after timestamp from database',
    );
    throw error;
  }
}

export async function saveSuggestions({
  suggestions,
}: {
  suggestions: Array<Suggestion>;
}) {
  try {
    return await db.insert(suggestion).values(suggestions);
  } catch (error) {
    console.error('Failed to save suggestions in database');
    throw error;
  }
}

export async function getSuggestionsByDocumentId({
  documentId,
}: {
  documentId: string;
}) {
  try {
    return await db
      .select()
      .from(suggestion)
      .where(and(eq(suggestion.documentId, documentId)));
  } catch (error) {
    console.error(
      'Failed to get suggestions by document version from database',
    );
    throw error;
  }
}

export async function getDocumentsByUserId({ id }: { id: string }) {
  try {
    // First, get all document IDs that have at least one row with deleted=true
    const deletedDocumentIds = await db
      .selectDistinct({ id: document.id })
      .from(document)
      .where(and(eq(document.userId, id), eq(document.deleted, true)));

    const deletedIds = deletedDocumentIds.map(doc => doc.id);

    // Build the where conditions for non-deleted documents
    const whereConditions = [
      eq(document.userId, id),
      eq(document.deleted, false)
    ];

    // If there are deleted document IDs, exclude them from the results
    if (deletedIds.length > 0) {
      whereConditions.push(notInArray(document.id, deletedIds));
    }

    // Get all non-deleted documents for the user
    const allDocuments = await db
      .select()
      .from(document)
      .where(and(...whereConditions))
      .orderBy(desc(document.createdAt));

    // Group by document ID and keep only the latest version (highest createdAt) of each
    const latestDocuments = new Map<string, typeof allDocuments[0]>();

    for (const doc of allDocuments) {
      const existingDoc = latestDocuments.get(doc.id);
      if (!existingDoc || new Date(doc.createdAt) > new Date(existingDoc.createdAt)) {
        latestDocuments.set(doc.id, doc);
      }
    }

    // Convert back to array and sort by createdAt ascending (oldest first, newest last)
    return Array.from(latestDocuments.values())
      .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
  } catch (error) {
    console.error('Failed to get documents by user from database');
    throw error;
  }
}

export async function softDeleteDocumentById({ id }: { id: string }) {
  try {
    // Soft delete by updating the deleted column instead of removing the record
    return await db
      .update(document)
      .set({ deleted: true })
      .where(eq(document.id, id));
  } catch (error) {
    console.error('Failed to soft delete document by id from database');
    throw error;
  }
}

export async function getMessageById({ id }: { id: string }) {
  try {
    return await db.select().from(message).where(eq(message.id, id));
  } catch (error) {
    console.error('Failed to get message by id from database');
    throw error;
  }
}

export async function deleteMessagesByChatIdAfterTimestamp({
  chatId,
  timestamp,
}: {
  chatId: string;
  timestamp: Date;
}) {
  try {
    return await db
      .delete(message)
      .where(
        and(eq(message.chatId, chatId), gte(message.createdAt, timestamp)),
      );
  } catch (error) {
    console.error(
      'Failed to delete messages by id after timestamp from database',
    );
    throw error;
  }
}

export async function updateChatVisiblityById({
  chatId,
  visibility,
}: {
  chatId: string;
  visibility: 'private' | 'public';
}) {
  try {
    return await db.update(chat).set({ visibility }).where(eq(chat.id, chatId));
  } catch (error) {
    console.error('Failed to update chat visibility in database');
    throw error;
  }
}

export async function getUserByResetToken(token: string) {
  const result = await db
    .select()
    .from(user)
    .where(
      and(
        eq(user.resetToken, token),
        // Ensure token hasn't expired
        sql`${user.resetTokenExpires} > NOW()`,
      ),
    )
    .limit(1);

  return result[0];
}
export async function updateUserResetToken(
  userId: string,
  resetToken: string | null,
  resetTokenExpires: Date | null,
) {
  await db
    .update(user)
    .set({
      resetToken,
      resetTokenExpires,
    })
    .where(eq(user.id, userId));
}
export async function updateUserPassword(
  userId: string,
  hashedPassword: string,
) {
  await db
    .update(user)
    .set({
      password: hashedPassword,
      resetToken: null,
      resetTokenExpires: null,
    })
    .where(eq(user.id, userId));
}
export async function getUserByEmail(email: string): Promise<User | null> {
  try {
    const [foundUser] = await db
      .select()
      .from(user)
      .where(eq(user.email, email));
    return foundUser || null;
  } catch (error) {
    console.error('Failed to get user by email from database');
    throw error;
  }
}

export async function createTemporaryUser(id: string) {
  try {
    const randomPassword = crypto.randomUUID();
    const hashedPassword = await hash(randomPassword, 10);

    return await db
      .insert(user)
      .values({
        email: `temp_${id}@temporary.user`,
        is_temporary: true,
        password: hashedPassword,
      })
      .returning();
  } catch (error) {
    console.error('Failed to create temporary user in database', error);
    throw error;
  }
}

export async function saveRefreshToken(token: {
  token: string;
  userId: string;
  sessionId: string;
  deviceInfo: string | null;
  expiresAt: Date;
}) {
  try {
    const values = {
      token: token.token,
      user_id: token.userId,
      session_id: token.sessionId,
      device_info: token.deviceInfo,
      expires_at: token.expiresAt,
      is_active: true,
      last_used_at: new Date(),
    };

    return await db
      .insert(refreshToken)
      .values(values)
      .onConflictDoUpdate({
        target: refreshToken.token,
        set: {
          user_id: sql`EXCLUDED.user_id`,
          session_id: sql`EXCLUDED.session_id`,
          device_info: sql`EXCLUDED.device_info`,
          expires_at: sql`EXCLUDED.expires_at`,
          is_active: true,
          last_used_at: new Date(),
        },
      });
  } catch (error) {
    console.error('Failed to save refresh token in database');
    throw error;
  }
}

export async function updateTemporaryUser({
  id,
  email,
  password,
}: {
  id: string;
  email: string;
  password: string | null;
}) {
  try {
    // First verify the temp user exists
    const tempUser = await db
      .select()
      .from(user)
      .where(and(eq(user.id, id), eq(user.is_temporary, true)));

    const updateData: any = {
      email: email,
      is_temporary: false,
    };

    if (password !== null) {
      updateData.password = password;
    }

    if (tempUser.length === 0) {
      // If no temporary user exists, create a new user
      return await db
        .insert(user)
        .values({
          id,
          email,
          password: password || undefined,
          is_temporary: false,
        })
        .returning();
    }

    // Update existing temporary user
    const updatedUser = await db
      .update(user)
      .set(updateData)
      .where(and(eq(user.id, id), eq(user.is_temporary, true)))
      .returning();

    return updatedUser;
  } catch (error) {
    console.error('Failed to update temporary user in database', error);
    throw error;
  }
}

export async function saveEmotionMessage({
  messageId,
  chatId,
  userId,
  emotions,
}: {
  messageId: string;
  chatId: string;
  userId: string;
  emotions: Record<string, number>;
}) {
  try {
    return await db.insert(messageEmotion).values({
      messageId,
      chatId,
      userId,
      emotions,
      createdAt: new Date(),
    });
  } catch (error) {
    console.error('Failed to save emotion message in database', error);
    throw error;
  }
}

export async function getMessageEmotions({ chatId }: { chatId: string }) {
  try {
    return await db
      .select({
        id: messageEmotion.id,
        messageId: messageEmotion.messageId,
        chatId: messageEmotion.chatId,
        userId: messageEmotion.userId,
        emotions: messageEmotion.emotions,
        createdAt: messageEmotion.createdAt,
      })
      .from(messageEmotion)
      .where(eq(messageEmotion.chatId, chatId));
  } catch (error) {
    console.error('Failed to get message emotions from database', error);
    throw error;
  }
}

export async function saveDebugMessages({
  messages,
}: { messages: Array<DebugMessageInsert> }) {
  try {
    return await db.insert(debugMessage).values(messages);
  } catch (error) {
    console.error('Failed to save debug messages in database', error);
    throw error;
  }
}

export async function getDebugMessagesByChatId({ id }: { id: string }) {
  try {
    return await db
      .select({
        id: debugMessage.id,
        createdAt: debugMessage.createdAt,
        chatId: debugMessage.chatId,
        role: debugMessage.role,
        content: debugMessage.content,
        modelId: debugMessage.modelId,
        systemPromptId: debugMessage.systemPromptId,
      })
      .from(debugMessage)
      .innerJoin(debugChat, eq(debugMessage.chatId, debugChat.id))
      .where(and(eq(debugMessage.chatId, id), eq(debugChat.deleted, false)))
      .orderBy(asc(debugMessage.createdAt));
  } catch (error) {
    console.error(
      'Failed to get debug messages by chat id from database',
      error,
    );
    throw error;
  }
}

export async function deleteDebugMessagesByChatIdAfterTimestamp({
  chatId,
  timestamp,
}: {
  chatId: string;
  timestamp: Date;
}) {
  try {
    return await db
      .delete(debugMessage)
      .where(
        and(
          eq(debugMessage.chatId, chatId),
          gte(debugMessage.createdAt, timestamp),
        ),
      );
  } catch (error) {
    console.error(
      'Failed to delete debug messages by id after timestamp from database',
    );
    throw error;
  }
}

export async function saveDebugChat({
  id,
  userId,
  title,
  isVoiceChat,
}: {
  id: string;
  userId: string;
  title: string;
  isVoiceChat?: boolean;
}) {
  try {
    return await db.insert(debugChat).values({
      id,
      userId,
      title,
      isVoiceChat: isVoiceChat ?? false,
    });
  } catch (error) {
    console.error('Failed to save debug chat in database');
    throw error;
  }
}

export async function getDebugChatById({ id }: { id: string }) {
  try {
    const [selectedChat] = await db
      .select()
      .from(debugChat)
      .where(and(eq(debugChat.id, id), eq(debugChat.deleted, false)));
    return selectedChat;
  } catch (error) {
    console.error('Failed to get debug chat by id from database');
    throw error;
  }
}

export async function deleteDebugChatById({ id }: { id: string }) {
  try {
    // Soft delete by updating the deleted column instead of removing the record
    return await db
      .update(debugChat)
      .set({ deleted: true })
      .where(eq(debugChat.id, id));
  } catch (error) {
    console.error('Failed to soft delete debug chat by id from database');
    throw error;
  }
}

export async function getBatchSummariesByUserId(userId: string) {
  try {
    return await db
      .select()
      .from(batches)
      .where(eq(batches.user_id, userId))
      .orderBy(desc(batches.last_message_ts));
  } catch (error) {
    console.error(
      'Failed to get batch summaries for user from database',
      error,
    );
    throw error;
  }
}

export async function getHourlyRecollectionsByUserId(userId: string) {
  try {
    // Calculate the timestamp for 48 hours ago
    const fortyEightHoursAgo = new Date();
    fortyEightHoursAgo.setHours(fortyEightHoursAgo.getHours() - 48);

    return await db
      .select()
      .from(recollections)
      .where(
        and(
          eq(recollections.user_id, userId),
          eq(recollections.granularity, 'hourly'),
          gte(recollections.period_start, fortyEightHoursAgo),
          // Exclude cleared recollections
          notExists(
            db.select()
              .from(dismissedRecollections)
              .where(
                and(
                  eq(dismissedRecollections.user_id, userId),
                  eq(dismissedRecollections.recollection_id, recollections.recollection_id)
                )
              )
          )
        )
      )
      .orderBy(desc(recollections.period_start));
  } catch (error) {
    console.error('Failed to get hourly recollections from database', JSON.stringify(error));
    throw error;
  }
}

export async function getDailyRecollectionsByUserId(userId: string) {
  try {
    // Calculate the start of current week (Sunday)
    const now = new Date();
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay()); // Go back to Sunday
    startOfWeek.setHours(0, 0, 0, 0);

    // Calculate 2 days ago
    const twoDaysAgo = new Date(now);
    twoDaysAgo.setDate(now.getDate() - 2);
    twoDaysAgo.setHours(0, 0, 0, 0);

    return await db
      .select()
      .from(recollections)
      .where(
        and(
          eq(recollections.user_id, userId),
          eq(recollections.granularity, 'daily'),
          gte(recollections.period_start, startOfWeek),
          lt(recollections.period_start, twoDaysAgo),
          // Exclude cleared recollections
          notExists(
            db.select()
              .from(dismissedRecollections)
              .where(
                and(
                  eq(dismissedRecollections.user_id, userId),
                  eq(dismissedRecollections.recollection_id, recollections.recollection_id)
                )
              )
          )
        )
      )
      .orderBy(desc(recollections.period_start));
  } catch (error) {
    console.error('Failed to get daily recollections from database', JSON.stringify(error));
    throw error;
  }
}

export async function getWeeklyRecollectionsByUserId(userId: string) {
  try {
    // Calculate the start of current week (Sunday)
    const now = new Date();
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay()); // Go back to Sunday
    startOfWeek.setHours(0, 0, 0, 0);

    // Calculate 3 months ago
    const threeMonthsAgo = new Date(now);
    threeMonthsAgo.setMonth(now.getMonth() - 3);
    threeMonthsAgo.setHours(0, 0, 0, 0);

    return await db
      .select()
      .from(recollections)
      .where(
        and(
          eq(recollections.user_id, userId),
          eq(recollections.granularity, 'weekly'),
          gte(recollections.period_start, threeMonthsAgo),
          lt(recollections.period_start, startOfWeek),
          // Exclude cleared recollections
          notExists(
            db.select()
              .from(dismissedRecollections)
              .where(
                and(
                  eq(dismissedRecollections.user_id, userId),
                  eq(dismissedRecollections.recollection_id, recollections.recollection_id)
                )
              )
          )
        )
      )
      .orderBy(desc(recollections.period_start));
  } catch (error) {
    console.error('Failed to get weekly recollections from database', JSON.stringify(error));
    throw error;
  }
}

export async function getMonthlyRecollectionsByUserId(userId: string) {
  try {
    // Calculate 3 months ago
    const now = new Date();
    const threeMonthsAgo = new Date(now);
    threeMonthsAgo.setMonth(now.getMonth() - 3);
    threeMonthsAgo.setHours(0, 0, 0, 0);

    return await db
      .select()
      .from(recollections)
      .where(
        and(
          eq(recollections.user_id, userId),
          eq(recollections.granularity, 'monthly'),
          lt(recollections.period_start, threeMonthsAgo),
          // Exclude cleared recollections
          notExists(
            db.select()
              .from(dismissedRecollections)
              .where(
                and(
                  eq(dismissedRecollections.user_id, userId),
                  eq(dismissedRecollections.recollection_id, recollections.recollection_id)
                )
              )
          )
        )
      )
      .orderBy(desc(recollections.period_start));
  } catch (error) {
    console.error('Failed to get monthly recollections from database', JSON.stringify(error));
    throw error;
  }
}

export async function getUserMessageCursor(userId: string) {
  try {
    return await db
      .select()
      .from(userMessageCursor)
      .where(eq(userMessageCursor.user_id, userId))
      .limit(1);
  } catch (error) {
    console.error('Failed to get user message cursor from database', error);
    throw error;
  }
}

export async function getUniqueNodeLabelsByUserId(userId: string) {
  try {
    const result = await db
      .selectDistinct({ label: nodes.label })
      .from(nodes)
      .where(eq(nodes.user_id, userId));
    return result.map((row) => row.label);
  } catch (error) {
    console.error('Failed to fetch unique node labels for user', error);
    throw error;
  }
}

export async function getNodeDetailsByLabel(userId: string, label: string) {
  try {
    // First get all nodes with this label
    const matchingNodes = await db
      .select({
        node_id: nodes.node_id,
        label: nodes.label,
        node_type: nodes.node_type,
        description: nodes.description,
      })
      .from(nodes)
      .where(and(eq(nodes.user_id, userId), eq(nodes.label, label)));

    // For each node, get its associated edges (both incoming and outgoing)
    const result = await Promise.all(
      matchingNodes.map(async (node) => {
        // Get outgoing edges
        const outgoingEdges = await db
          .select({
            edge_id: edges.edge_id,
            batch_id: edges.batch_id,
            intensity: edges.intensity,
            valence: edges.valence,
            variance: edges.variance,
            timeframe_start: edges.timeframe_start,
            timeframe_end: edges.timeframe_end,
            summary: edges.summary,
            target_node: {
              node_id: nodes.node_id,
              label: nodes.label,
              node_type: nodes.node_type,
              description: nodes.description,
            },
          })
          .from(edges)
          .innerJoin(nodes, eq(edges.to_node_id, nodes.node_id))
          .where(
            and(
              eq(edges.from_node_id, node.node_id),
              eq(edges.user_id, userId),
            ),
          );

        // Get incoming edges
        const incomingEdges = await db
          .select({
            edge_id: edges.edge_id,
            batch_id: edges.batch_id,
            intensity: edges.intensity,
            valence: edges.valence,
            variance: edges.variance,
            timeframe_start: edges.timeframe_start,
            timeframe_end: edges.timeframe_end,
            summary: edges.summary,
            source_node: {
              node_id: nodes.node_id,
              label: nodes.label,
              node_type: nodes.node_type,
              description: nodes.description,
            },
          })
          .from(edges)
          .innerJoin(nodes, eq(edges.from_node_id, nodes.node_id))
          .where(
            and(eq(edges.to_node_id, node.node_id), eq(edges.user_id, userId)),
          );

        return {
          ...node,
          outgoing_edges: outgoingEdges,
          incoming_edges: incomingEdges,
        };
      }),
    );
    return result;
  } catch (error) {
    console.error('Failed to fetch node details:', error);
    throw error;
  }
}

// Add connection cleanup
export async function closeConnection() {
  try {
    await client.end();
    console.log('Database connection closed gracefully');
  } catch (error) {
    console.error('Error closing database connection:', error);
  }
}

// Handle process termination
if (typeof process !== 'undefined') {
  process.on('SIGTERM', closeConnection);
  process.on('SIGINT', closeConnection);
}

export async function getAllMessagesByUserId(userId: string, limit: number= 100, offset:number=0) {
  return withRetry(
    async () => {
      // Get user's cleared messages settings
      const [clearedSettings] = await db
        .select()
        .from(clearedMessages)
        .where(eq(clearedMessages.userId, userId))
        .orderBy(desc(clearedMessages.clearedAt))
        .limit(1);

      const whereConditions = [
        eq(chat.userId, userId),
        eq(chat.deleted, false)
      ];

      // If user has cleared messages, only show messages after the last cleared message
      // and exclude the last cleared message itself
      if (clearedSettings?.lastClearedMessageId) {
        // Get the timestamp of the last cleared message
        const [lastClearedMessage] = await db
          .select({ createdAt: message.createdAt })
          .from(message)
          .where(eq(message.id, clearedSettings.lastClearedMessageId))
          .limit(1);

        if (lastClearedMessage?.createdAt) {
          // Use gte (greater than or equal) to include messages at the same timestamp
          // but exclude the specific cleared message by ID
          whereConditions.push(gte(message.createdAt, lastClearedMessage.createdAt));
          whereConditions.push(ne(message.id, clearedSettings.lastClearedMessageId));
        }
      }

      return await db
        .select({
          id: message.id,
          createdAt: message.createdAt,
          chatId: message.chatId,
          role: message.role,
          content: message.content,
          modelId: message.modelId,
          systemPromptId: message.systemPromptId,
        })
        .from(message)
        .innerJoin(chat, eq(message.chatId, chat.id))
        .where(and(...whereConditions))
        .limit(limit).offset(offset)
        .orderBy(desc(message.createdAt));
    },
    'getAllMessagesByUserId'
  ).catch((error) => {
    console.error('Failed to get all messages by user id from database', {
      userId,
      error: error?.message,
      code: error?.code,
    });
    throw error;
  });
}

export async function saveUserMessageVisibility({
  userId,
  clearedUntilTimestamp,
}: {
  userId: string;
  clearedUntilTimestamp: Date;
}) {
  try {
    return await db.insert(userMessageVisibility).values({
      userId,
      clearedUntilTimestamp,
    });
  } catch (error) {
    console.error('Failed to save user message visibility in database', error);
    throw error;
  }
}

export async function getUserMessageVisibility({ userId }: { userId: string }) {
  try {
    const [settings] = await db
      .select()
      .from(userMessageVisibility)
      .where(eq(userMessageVisibility.userId, userId))
      .orderBy(desc(userMessageVisibility.createdAt))
      .limit(1);
    return settings;
  } catch (error) {
    console.error('Failed to get user message visibility from database', error);
    throw error;
  }
}

export async function saveClearedMessages({
  userId,
  lastClearedMessageId,
}: {
  userId: string;
  lastClearedMessageId: string;
}) {
  try {
    return await db.insert(clearedMessages).values({
      userId,
      lastClearedMessageId,
    });
  } catch (error) {
    console.error('Failed to save cleared messages in database', error);
    throw error;
  }
}

export async function getClearedMessages({ userId }: { userId: string }) {
  try {
    const [settings] = await db
      .select()
      .from(clearedMessages)
      .where(eq(clearedMessages.userId, userId))
      .orderBy(desc(clearedMessages.clearedAt))
      .limit(1);
    return settings;
  } catch (error) {
    console.error('Failed to get cleared messages from database', error);
    throw error;
  }
}

// Recollection clear functions
export async function clearRecollection({
  userId,
  recollectionId,
}: {
  userId: string;
  recollectionId: string;
}) {
  try {
    return await db.insert(dismissedRecollections).values({
      user_id: userId,
      recollection_id: recollectionId,
    });
  } catch (error) {
    console.error('Failed to clear recollection in database', error);
    throw error;
  }
}

export async function unclearRecollection({
  userId,
  recollectionId,
}: {
  userId: string;
  recollectionId: string;
}) {
  try {
    return await db
      .delete(dismissedRecollections)
      .where(
        and(
          eq(dismissedRecollections.user_id, userId),
          eq(dismissedRecollections.recollection_id, recollectionId)
        )
      );
  } catch (error) {
    console.error('Failed to unclear recollection in database', error);
    throw error;
  }
}

export async function isRecollectionCleared({
  userId,
  recollectionId,
}: {
  userId: string;
  recollectionId: string;
}): Promise<boolean> {
  try {
    const [cleared] = await db
      .select()
      .from(dismissedRecollections)
      .where(
        and(
          eq(dismissedRecollections.user_id, userId),
          eq(dismissedRecollections.recollection_id, recollectionId)
        )
      )
      .limit(1);
    return !!cleared;
  } catch (error) {
    console.error('Failed to check if recollection is cleared', error);
    throw error;
  }
}
