import type { InferSelectModel } from 'drizzle-orm';
import {
  pgTable,
  varchar,
  timestamp,
  json,
  uuid,
  text,
  primaryKey,
  foreignKey,
  boolean,
  pgSchema,
  date,
  unique,
} from 'drizzle-orm/pg-core';

export const user = pgTable('User', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  email: varchar('email', { length: 64 }).notNull(),
  password: varchar('password', { length: 64 }),
  resetToken: text('reset_token').unique(),
  resetTokenExpires: timestamp('reset_token_expires'),
  is_temporary: boolean().default(false),
  googleId: varchar('google_id', { length: 64 }).unique(),
  googleAccessToken: text('google_access_token'),
  googleRefreshToken: text('google_refresh_token'),
  googleTokenExpiry: timestamp('google_token_expiry'),
  created_at: timestamp('created_at').notNull().defaultNow(),
});

export type User = InferSelectModel<typeof user>;

export const chat = pgTable('Chat', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  title: text('title').notNull(),
  userId: uuid('userId')
    .notNull()
    .references(() => user.id),
  visibility: varchar('visibility', { enum: ['public', 'private'] })
    .notNull()
    .default('private'),
  deleted: boolean('deleted').notNull().default(false),
  isVoiceChat: boolean('isVoiceChat').notNull().default(false),
});



export const debugChat = pgTable('DebugChat', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  title: text('title').notNull(),
  userId: uuid('userId')
    .notNull()
    .references(() => user.id),
  visibility: varchar('visibility', { enum: ['public', 'private'] })
    .notNull()
    .default('private'),
  deleted: boolean('deleted').notNull().default(false),
  isVoiceChat: boolean('isVoiceChat').notNull().default(false),
});



export const message = pgTable('Message', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  chatId: uuid('chatId')
    .notNull()
    .references(() => chat.id),
  role: varchar('role').notNull(),
  content: json('content').notNull(),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  modelId: varchar('modelId'),
  systemPromptId: varchar('systemPromptId'),
  enhancedSystemPrompt: text('enhancedSystemPrompt'),
});

export const debugMessage = pgTable('DebugMessage', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  chatId: uuid('chatId')
    .notNull()
    .references(() => debugChat.id),
  role: varchar('role').notNull(),
  content: json('content').notNull(),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
  modelId: varchar('modelId'),
  systemPromptId: varchar('systemPromptId'),
  enhancedSystemPrompt: text('enhancedSystemPrompt'),
});

export const messageEmotion = pgTable('MessageEmotion', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  messageId: uuid('messageId')
    .notNull()
    .references(() => message.id),
  chatId: uuid('chatId')
    .notNull()
    .references(() => chat.id),
  userId: uuid('userId')
    .notNull()
    .references(() => user.id),
  emotions: json('emotions').notNull(),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
});

export type Message = InferSelectModel<typeof message>;
export type DebugMessage = InferSelectModel<typeof debugMessage>;
export type Chat = InferSelectModel<typeof chat>;
export type DebugChat = InferSelectModel<typeof debugChat>;
export type Document = InferSelectModel<typeof document>;
export type Suggestion = InferSelectModel<typeof suggestion>;

// Insert types with optional createdAt (since it's auto-generated by DB)
// Note: Document and Suggestion keep createdAt required since they have special constraints
export type MessageInsert = Omit<Message, 'createdAt'> & { createdAt?: Date };
export type DebugMessageInsert = Omit<DebugMessage, 'createdAt'> & { createdAt?: Date };
export type ChatInsert = Omit<Chat, 'createdAt'> & { createdAt?: Date };
export type DebugChatInsert = Omit<DebugChat, 'createdAt'> & { createdAt?: Date };

// Type for query results where enhancedSystemPrompt might not be selected
export type MessageQueryResult = Omit<Message, 'enhancedSystemPrompt'> & {
  enhancedSystemPrompt?: string | null
};
export type DebugMessageQueryResult = Omit<DebugMessage, 'enhancedSystemPrompt'> & {
  enhancedSystemPrompt?: string | null
};

export const vote = pgTable(
  'Vote',
  {
    chatId: uuid('chatId')
      .notNull()
      .references(() => chat.id),
    messageId: uuid('messageId')
      .notNull()
      .references(() => message.id),
    isUpvoted: boolean('isUpvoted').notNull(),
  },
  (table) => {
    return {
      pk: primaryKey({ columns: [table.chatId, table.messageId] }),
    };
  },
);

export type Vote = InferSelectModel<typeof vote>;

export const document = pgTable(
  'Document',
  {
    id: uuid('id').notNull().defaultRandom(),
    createdAt: timestamp('createdAt').notNull(),
    title: text('title').notNull(),
    content: text('content'),
    userId: uuid('userId')
      .notNull()
      .references(() => user.id),
    deleted: boolean('deleted').notNull().default(false),
  },
  (table) => {
    return {
      pk: primaryKey({ columns: [table.id, table.createdAt] }),
    };
  },
);



export const suggestion = pgTable(
  'Suggestion',
  {
    id: uuid('id').notNull().defaultRandom(),
    documentId: uuid('documentId').notNull(),
    documentCreatedAt: timestamp('documentCreatedAt').notNull(),
    originalText: text('originalText').notNull(),
    suggestedText: text('suggestedText').notNull(),
    description: text('description'),
    isResolved: boolean('isResolved').notNull().default(false),
    userId: uuid('userId')
      .notNull()
      .references(() => user.id),
    createdAt: timestamp('createdAt').notNull(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.id] }),
    documentRef: foreignKey({
      columns: [table.documentId, table.documentCreatedAt],
      foreignColumns: [document.id, document.createdAt],
    }),
  }),
);



export const refreshToken = pgTable('RefreshToken', {
  token: text('token').primaryKey(),
  user_id: uuid('user_id').references(() => user.id),
  session_id: uuid('session_id').unique().notNull(),
  device_info: text('device_info'),
  expires_at: timestamp('expires_at').notNull(),
  is_active: boolean('is_active').notNull().default(true),
  last_used_at: timestamp('last_used_at').notNull(),
});

export type RefreshToken = InferSelectModel<typeof refreshToken>;

const profileSchema = pgSchema('profile');

export const batches = profileSchema.table('batches', {
  batch_id: uuid('batch_id').primaryKey().notNull(),
  user_id: uuid('user_id')
    .notNull()
    .references(() => user.id),
  last_message_ts: timestamp('last_message_ts').notNull().defaultNow(),
  title: varchar('title', { length: 200 }),
  summary: text('summary'),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
  messages_processed: text('messages_processed').notNull().default('0'),
  first_message_id: uuid('first_message_id'),
  last_message_id: uuid('last_message_id'),
});

export type Batches = InferSelectModel<typeof batches>;

export const recollections = profileSchema.table('recollections', {
  recollection_id: uuid('recollection_id').primaryKey().notNull().defaultRandom(),
  user_id: uuid('user_id')
    .notNull()
    .unique()
    .references(() => user.id),
  summary: text('summary').notNull(),
  granularity: varchar('granularity', {
    length: 20,
    enum: ['hourly', 'daily', 'weekly', 'monthly'],
  })
    .notNull()
    .unique(),
  period_start: timestamp('period_start').notNull().unique(),
  period_end: timestamp('period_end').notNull(),
  created_at: timestamp('created_at').defaultNow(),
  updated_at: timestamp('updated_at').defaultNow(),
});

export type Recollection = InferSelectModel<typeof recollections>;

export const dismissedRecollections = profileSchema.table('dismissed_recollections', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  user_id: uuid('user_id')
    .notNull()
    .references(() => user.id, { onDelete: 'cascade' }),
  recollection_id: uuid('recollection_id')
    .notNull()
    .references(() => recollections.recollection_id, { onDelete: 'cascade' }),
  dismissed_at: timestamp('dismissed_at').notNull().defaultNow(),
}, (table) => {
  return {
    // Ensure a user can only dismiss a recollection once
    uniqueUserRecollection: unique().on(table.user_id, table.recollection_id),
  };
});

export type DismissedRecollection = InferSelectModel<typeof dismissedRecollections>;
export type UserMessageVisibility = InferSelectModel<typeof userMessageVisibility>;
export type UserMessageVisibilityInsert = Omit<UserMessageVisibility, 'createdAt'> & { createdAt?: Date };
export type ClearedMessages = InferSelectModel<typeof clearedMessages>;
export type ClearedMessagesInsert = Omit<ClearedMessages, 'id' | 'clearedAt'> & {
  id?: string;
  clearedAt?: Date;
};

export const userMessageCursor = profileSchema.table('user_message_cursor', {
  user_id: uuid('user_id')
    .primaryKey()
    .notNull()
    .references(() => user.id, { onDelete: 'cascade' }),
  last_message_id: uuid('last_message_id').notNull(),
  last_message_ts: timestamp('last_message_ts'),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

export const userMessageVisibility = pgTable('UserMessageVisibility', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  userId: uuid('userId')
    .notNull()
    .references(() => user.id, { onDelete: 'cascade' }),
  clearedUntilTimestamp: timestamp('clearedUntilTimestamp').notNull(),
  createdAt: timestamp('createdAt').notNull().defaultNow(),
});

export const clearedMessages = pgTable('ClearedMessages', {
  id: uuid('id').primaryKey().notNull().defaultRandom(),
  userId: uuid('user_id')
    .notNull()
    .references(() => user.id, { onDelete: 'cascade' }),
  lastClearedMessageId: uuid('last_cleared_message_id')
    .notNull()
    .references(() => message.id, { onDelete: 'cascade' }),
  clearedAt: timestamp('cleared_at').notNull().defaultNow(),
});

export const nodes = profileSchema.table('nodes', {
  node_id: uuid('node_id').primaryKey().notNull(),
  user_id: uuid('user_id')
    .notNull()
    .references(() => user.id),
  label: varchar('label', { length: 255 }),
  node_type: varchar('node_type', { length: 255 }),
  description: text('description'),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});

export const edges = profileSchema.table('edges', {
  edge_id: uuid('edge_id').primaryKey().notNull(),
  batch_id: uuid('batch_id')
    .notNull()
    .references(() => batches.batch_id),
  user_id: uuid('user_id')
    .notNull()
    .references(() => user.id),
  from_node_id: uuid('from_node_id')
    .notNull()
    .references(() => nodes.node_id),
  to_node_id: uuid('to_node_id')
    .notNull()
    .references(() => nodes.node_id),
  intensity: text('intensity'),
  valence: text('valence'),
  variance: text('variance'),
  timeframe_start: date('timeframe_start'),
  timeframe_end: date('timeframe_end'),
  summary: text('summary'),
  created_at: timestamp('created_at').notNull().defaultNow(),
  updated_at: timestamp('updated_at').notNull().defaultNow(),
});
