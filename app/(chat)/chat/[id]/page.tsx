import { notFound } from 'next/navigation';

import { auth } from '@/app/(auth)/auth';
import { Chat } from '@/components/chat';
import { DEFAULT_MODEL_NAME, models } from '@/lib/ai/models';
import { getChatById, getMessagesByChatId } from '@/lib/db/queries';
import { convertToUIMessages } from '@/lib/utils';
import { getTempUserId, getModelId } from '@/app/actions/cookies';

export default async function Page(props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  const { id } = params;
  const chat = await getChatById({ id });

  if (!chat) {
    notFound();
  }

  const session = await auth();
  const tempUserId = await getTempUserId();

  if (chat.visibility === 'private') {
    if (!session?.user && !tempUserId) {
      return notFound();
    }

    if (session?.user?.id !== chat.userId && tempUserId !== chat.userId) {
      return notFound();
    }
  }

  const userId = session?.user?.id || tempUserId;
  const messagesFromDb = await getMessagesByChatId({
    id,
    userId,
    limit: 50,
    offset: 0,
    orderBy: 'desc',
  });

  const modelIdFromCookie = await getModelId();
  const selectedModelId =
    models.find((model) => model.id === modelIdFromCookie)?.id ||
    DEFAULT_MODEL_NAME;

  return (
    <Chat
      id={chat.id}
      initialMessages={convertToUIMessages([...messagesFromDb].reverse())}
      selectedModelId={selectedModelId}
      selectedVisibilityType={chat.visibility}
      isReadonly={
        chat.isVoiceChat ||
        (session?.user?.id !== chat.userId && tempUserId !== chat.userId)
      }
      isUserTemporary={!session?.user?.id}
    />
  );
}
