import { auth } from '@/app/(auth)/auth';
import { getVotesByChatId, voteMessage } from '@/lib/db/queries';
import { cookies } from 'next/headers';
import { isValidUUID } from '@/lib/utils';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const chatId = searchParams.get('chatId');

  if (!chatId) {
    return new Response('chatId is required', { status: 400 });
  }

  // Validate that chatId is a valid UUID
  if (!isValidUUID(chatId)) {
    console.error('Invalid chatId format:', chatId);
    return new Response('Invalid chatId format', { status: 400 });
  }

  const session = await auth();
  const cookieStore = await cookies();
  const tempUserId = cookieStore.get('tempUserId')?.value;

  if (!session?.user?.email && !tempUserId) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const votes = await getVotesByChatId({ id: chatId });
    return Response.json(votes, { status: 200 });
  } catch (error) {
    console.error('Failed to get votes:', error);
    return new Response('Failed to get votes', { status: 500 });
  }
}

export async function PATCH(request: Request) {
  const {
    chatId,
    messageId,
    type,
  }: { chatId: string; messageId: string; type: 'up' | 'down' } =
    await request.json();

  if (!chatId || !messageId || !type) {
    return new Response('messageId and type are required', { status: 400 });
  }

  // Validate that both chatId and messageId are valid UUIDs
  if (!isValidUUID(chatId)) {
    console.error('Invalid chatId format:', chatId);
    return new Response('Invalid chatId format', { status: 400 });
  }

  if (!isValidUUID(messageId)) {
    console.error('Invalid messageId format:', messageId);
    return new Response('Invalid messageId format', { status: 400 });
  }

  const session = await auth();
  const cookieStore = await cookies();
  const tempUserId = cookieStore.get('tempUserId')?.value;

  if (!session?.user?.email && !tempUserId) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    await voteMessage({
      chatId,
      messageId,
      type: type,
    });

    return new Response('Message voted', { status: 200 });
  } catch (error) {
    console.error('Failed to vote message:', error);
    return new Response('Failed to vote message', { status: 500 });
  }
}
