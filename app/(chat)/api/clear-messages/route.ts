import { auth } from '@/app/(auth)/auth';
import { saveClearedMessages, getAllMessagesByUserId } from '@/lib/db/queries';
import { getTempUserId } from '@/app/actions/cookies';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    const tempUserId = await getTempUserId();
    
    // Get user ID from either session or temporary user
    const userId = session?.user?.id || tempUserId;

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the user's most recent message to use as the last cleared message
    const recentMessages = await getAllMessagesByUserId(userId, 1, 0);

    if (recentMessages.length === 0) {
      // If there are no messages, consider it already cleared
      return NextResponse.json({
        success: true,
        message: 'No messages to clear. Chat is already empty.',
      });
    }

    const lastMessageId = recentMessages[0].id;

    // Save the cleared messages record
    await saveClearedMessages({
      userId: userId,
      lastClearedMessageId: lastMessageId,
    });

    return NextResponse.json({
      success: true,
      message: 'Messages cleared from UI successfully. Note: Messages are still stored in AI memory for context.',
    });
  } catch (error) {
    console.error('Error clearing messages:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
