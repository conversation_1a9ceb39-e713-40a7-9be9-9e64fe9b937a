import type { ChatRequestOptions, Message } from 'ai';
import { PreviewMessage, ThinkingMessage } from './message';
import { Overview } from './overview';
import type { UIBlock } from './block';
import { type Dispatch, memo, type SetStateAction } from 'react';
import type { Vote } from '@/lib/db/schema';

interface MessagesProps {
  chatId: string;
  block: UIBlock;
  setBlock: Dispatch<SetStateAction<UIBlock>>;
  isLoading: boolean;
  votes: Array<Vote> | undefined;
  messages: Array<Message>;
  setMessages: (
    messages: Message[] | ((messages: Message[]) => Message[]),
  ) => void;
  reload: (
    chatRequestOptions?: ChatRequestOptions,
  ) => Promise<string | null | undefined>;
  isReadonly: boolean;
  isUserTemporary?: boolean;
}

function PureMessages({
  chatId,
  block,
  setBlock,
  isLoading,
  votes,
  messages,
  setMessages,
  reload,
  isReadonly,
  isUserTemporary,
}: MessagesProps) {
  return (
    <div
      className="flex flex-col min-w-0 gap-4 md:gap-6 flex-1 pt-3 md:pt-4 px-2 md:px-0"
    >
      {isUserTemporary && (
        <div className="sticky top-0 z-10 bg-yellow-50 border-l-4 border-yellow-400 p-3 md:p-4 mb-3 md:mb-4 mx-2 md:mx-0 rounded-r">
          <div className="flex">
            <div className="flex-1">
              <p className="text-xs md:text-sm text-yellow-700">
                Sign in to save your chat history, access features like audio,
                and more.{' '}
                <a
                  href="/login"
                  className="font-medium text-yellow-700 underline hover:text-yellow-600"
                >
                  Login
                </a>{' '}
                or{' '}
                <a
                  href="/register"
                  className="font-medium text-yellow-700 underline hover:text-yellow-600"
                >
                  Sign Up
                </a>
              </p>
            </div>
          </div>
        </div>
      )}

      {messages.length === 0 && <Overview />}

      {messages.map((message, index) => (
        <PreviewMessage
          key={message.id}
          chatId={chatId}
          message={message}
          block={block}
          setBlock={setBlock}
          isLoading={isLoading && messages.length - 1 === index}
          vote={
            votes
              ? votes.find((vote) => vote.messageId === message.id)
              : undefined
          }
          setMessages={setMessages}
          reload={reload}
          isReadonly={isReadonly}
        />
      ))}

      {isLoading &&
        messages.length > 0 &&
        messages[messages.length - 1].role === 'user' && <ThinkingMessage />}

      <div
        className="shrink-0 min-w-[24px] min-h-[24px]"
      />
    </div>
  );
}

function areEqual(prevProps: MessagesProps, nextProps: MessagesProps) {
  if (
    prevProps.block.status === 'streaming' &&
    nextProps.block.status === 'streaming'
  ) {
    return true;
  }

  return false;
}

export const Messages = memo(PureMessages, areEqual);
