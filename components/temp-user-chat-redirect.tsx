'use client';

import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useEffect } from 'react';

export function TempUserChatRedirect() {
  const router = useRouter();
  const { data: session, status } = useSession();

  useEffect(() => {
    // Wait for session to be loaded
    if (status === 'loading') return;

    // If user is authenticated, clear any stored temporary chat ID and don't redirect
    if (session?.user?.id) {
      localStorage.removeItem('tempUserChatId');
      return;
    }

    // Only for temporary users: check if there's a stored chat ID
    const storedChatId = localStorage.getItem('tempUserChatId');

    if (storedChatId) {
      // Redirect to the existing chat
      router.replace(`/chat/${storedChatId}`);
    }
  }, [router, session?.user?.id, status]);

  // This component doesn't render anything visible
  return null;
}
